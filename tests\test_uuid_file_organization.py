#!/usr/bin/env python3
"""tests for uuid-based file organization system."""

import os
import tempfile
import shutil
import uuid
import json
from unittest.mock import Mock, patch
from pathlib import Path
import sys

# add the project root to the path so we can import modules
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.dubbing import Dubber
from src.utils.run_manager import RunManager, RunInfo
from src.utils.logger import logger


class TestUuidFileOrganization:
    """test suite for uuid-based file organization."""

    def setup_method(self):
        """set up test environment before each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_input_file = os.path.join(self.temp_dir, "test_video.mp4")
        self.test_output_dir = os.path.join(self.temp_dir, "outputs")

        # create a dummy input file
        with open(self.test_input_file, "w") as f:
            f.write("dummy video content")

        os.makedirs(self.test_output_dir, exist_ok=True)

    def teardown_method(self):
        """clean up test environment after each test."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_dubber_generates_uuid(self):
        """test that dubber generates a valid uuid4 identifier."""
        with patch.multiple(
            "src.core.dubbing.Dubber",
            _verify_api_access=Mock(),
            run_preprocessing=Mock(),
            run_speech_to_text=Mock(),
            run_translation=Mock(),
            run_configure_text_to_speech=Mock(),
            run_text_to_speech=Mock(),
            run_postprocessing=Mock(),
            run_generate_subtitles=Mock(),
            _save_utterances=Mock(),
            run_cleaning=Mock(),
        ):
            dubber = Dubber(
                input_file=self.test_input_file,
                output_directory=self.test_output_dir,
                target_language="spa",
            )

            # check that run_id is generated and is a valid uuid4
            assert hasattr(dubber, "run_id")
            assert dubber.run_id is not None

            # validate uuid4 format
            try:
                uuid_obj = uuid.UUID(dubber.run_id, version=4)
                assert str(uuid_obj) == dubber.run_id
            except ValueError:
                assert False, f"generated run_id {dubber.run_id} is not a valid uuid4"

    def test_run_specific_directory_creation(self):
        """test that run-specific directories are created correctly."""
        with patch.multiple(
            "src.core.dubbing.Dubber",
            _verify_api_access=Mock(),
            run_preprocessing=Mock(),
            run_speech_to_text=Mock(),
            run_translation=Mock(),
            run_configure_text_to_speech=Mock(),
            run_text_to_speech=Mock(),
            run_postprocessing=Mock(),
            run_generate_subtitles=Mock(),
            _save_utterances=Mock(),
            run_cleaning=Mock(),
        ):
            dubber = Dubber(
                input_file=self.test_input_file,
                output_directory=self.test_output_dir,
                target_language="spa",
            )

            # check that run-specific directory exists
            expected_run_dir = os.path.join(self.test_output_dir, dubber.run_id)
            assert os.path.exists(expected_run_dir)
            assert os.path.isdir(expected_run_dir)

            # check that output_directory points to run-specific directory
            assert dubber.output_directory == expected_run_dir
            assert dubber.get_run_output_directory() == expected_run_dir
            assert dubber.get_base_output_directory() == self.test_output_dir

    def test_run_manager_registration(self):
        """test that runs are properly registered in the run manager."""
        with patch.multiple(
            "src.core.dubbing.Dubber",
            _verify_api_access=Mock(),
            run_preprocessing=Mock(),
            run_speech_to_text=Mock(),
            run_translation=Mock(),
            run_configure_text_to_speech=Mock(),
            run_text_to_speech=Mock(),
            run_postprocessing=Mock(),
            run_generate_subtitles=Mock(),
            _save_utterances=Mock(),
            run_cleaning=Mock(),
        ):
            dubber = Dubber(
                input_file=self.test_input_file,
                output_directory=self.test_output_dir,
                target_language="spa",
                source_language="eng",
            )

            # check that run is registered
            run_manager = RunManager(self.test_output_dir)
            run_info = run_manager.get_run_info(dubber.run_id)

            assert run_info is not None
            assert run_info.run_id == dubber.run_id
            assert run_info.input_file == self.test_input_file
            assert run_info.target_language == "spa"
            assert run_info.source_language == "eng"
            assert run_info.status == "running"

    def test_multiple_runs_isolation(self):
        """test that multiple runs create separate directories."""
        with patch.multiple(
            "src.core.dubbing.Dubber",
            _verify_api_access=Mock(),
            run_preprocessing=Mock(),
            run_speech_to_text=Mock(),
            run_translation=Mock(),
            run_configure_text_to_speech=Mock(),
            run_text_to_speech=Mock(),
            run_postprocessing=Mock(),
            run_generate_subtitles=Mock(),
            _save_utterances=Mock(),
            run_cleaning=Mock(),
        ):
            # create two dubber instances
            dubber1 = Dubber(
                input_file=self.test_input_file,
                output_directory=self.test_output_dir,
                target_language="spa",
            )

            dubber2 = Dubber(
                input_file=self.test_input_file,
                output_directory=self.test_output_dir,
                target_language="fra",
            )

            # check that they have different run ids
            assert dubber1.run_id != dubber2.run_id

            # check that they have different output directories
            assert dubber1.output_directory != dubber2.output_directory

            # check that both directories exist
            assert os.path.exists(dubber1.output_directory)
            assert os.path.exists(dubber2.output_directory)

            # check that both are registered
            run_manager = RunManager(self.test_output_dir)
            runs = run_manager.list_runs()
            assert len(runs) == 2

            run_ids = [run.run_id for run in runs]
            assert dubber1.run_id in run_ids
            assert dubber2.run_id in run_ids

    def test_run_manager_file_scanning(self):
        """test that run manager can scan and track files."""
        run_manager = RunManager(self.test_output_dir)

        # create a test run directory with some files
        test_run_id = str(uuid.uuid4())
        test_run_dir = os.path.join(self.test_output_dir, test_run_id)
        os.makedirs(test_run_dir, exist_ok=True)

        # create some test files
        test_files = [
            "dubbed_audio_spa.mp3",
            "dubbed_vocals.mp3",
            "spa.srt",
            "eng.srt",
            "utterance_metadata.json",
        ]

        for filename in test_files:
            file_path = os.path.join(test_run_dir, filename)
            with open(file_path, "w") as f:
                f.write(f"test content for {filename}")

        # scan files
        scanned_files = run_manager.scan_run_files(test_run_id)

        # check that all files were found
        assert len(scanned_files) == len(test_files)
        for filename in test_files:
            assert filename in scanned_files

    def test_run_cleanup(self):
        """test that run cleanup works properly."""
        run_manager = RunManager(self.test_output_dir)

        # create a test run
        test_run_id = str(uuid.uuid4())
        test_run_dir = os.path.join(self.test_output_dir, test_run_id)
        os.makedirs(test_run_dir, exist_ok=True)

        # register the run
        run_info = RunInfo(
            run_id=test_run_id,
            created_at="2023-01-01t00:00:00",
            input_file="test.mp4",
            source_language="eng",
            target_language="spa",
            status="completed",
            output_directory=test_run_dir,
            files_created=[],
            metadata={},
        )
        run_manager.register_run(run_info)

        # create some test files
        test_file = os.path.join(test_run_dir, "test_file.txt")
        with open(test_file, "w") as f:
            f.write("test content")

        # cleanup without removing files
        success = run_manager.cleanup_run(test_run_id, remove_files=False)
        assert success

        # check that directory still exists but run is not in registry
        assert os.path.exists(test_run_dir)
        assert run_manager.get_run_info(test_run_id) is None

        # register again and cleanup with file removal
        run_manager.register_run(run_info)
        success = run_manager.cleanup_run(test_run_id, remove_files=True)
        assert success

        # check that directory is removed
        assert not os.path.exists(test_run_dir)
        assert run_manager.get_run_info(test_run_id) is None


def run_tests():
    """run all tests."""
    test_instance = TestUuidFileOrganization()

    tests = [
        test_instance.test_dubber_generates_uuid,
        test_instance.test_run_specific_directory_creation,
        test_instance.test_run_manager_registration,
        test_instance.test_multiple_runs_isolation,
        test_instance.test_run_manager_file_scanning,
        test_instance.test_run_cleanup,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            test_instance.setup_method()
            test()
            test_instance.teardown_method()
            print(f"✓ {test.__name__}")
            passed += 1
        except Exception as e:
            print(f"✗ {test.__name__}: {e}")
            failed += 1
            test_instance.teardown_method()

    print(f"\ntest results: {passed} passed, {failed} failed")
    return failed == 0


def test_file_organization_integration():
    """integration test to verify files are organized correctly during dubbing."""
    temp_dir = tempfile.mkdtemp()

    try:
        test_input_file = os.path.join(temp_dir, "test_video.mp4")
        test_output_dir = os.path.join(temp_dir, "outputs")

        # create a dummy input file
        with open(test_input_file, "w") as f:
            f.write("dummy video content")

        os.makedirs(test_output_dir, exist_ok=True)

        # create a mock dubber that simulates file creation
        with patch.multiple(
            "src.core.dubbing.Dubber",
            _verify_api_access=Mock(),
            run_preprocessing=Mock(),
            run_speech_to_text=Mock(),
            run_translation=Mock(),
            run_configure_text_to_speech=Mock(),
            run_text_to_speech=Mock(),
            run_postprocessing=Mock(),
            run_generate_subtitles=Mock(),
            _save_utterances=Mock(),
            run_cleaning=Mock(),
        ):
            dubber = Dubber(
                input_file=test_input_file,
                output_directory=test_output_dir,
                target_language="spa",
                source_language="eng",
            )

            # simulate file creation in the run-specific directory
            test_files = [
                "dubbed_audio_spa.mp3",
                "dubbed_vocals.mp3",
                "spa.srt",
                "eng.srt",
                "utterance_metadata.json",
                "test_video_audio.wav",
                "test_video_video.mp4",
            ]

            for filename in test_files:
                file_path = os.path.join(dubber.output_directory, filename)
                with open(file_path, "w") as f:
                    f.write(f"test content for {filename}")

            # verify files are in the correct run-specific directory
            for filename in test_files:
                expected_path = os.path.join(test_output_dir, dubber.run_id, filename)
                assert os.path.exists(
                    expected_path
                ), f"file {filename} not found in run directory"

            # verify files are not in the base output directory
            for filename in test_files:
                base_path = os.path.join(test_output_dir, filename)
                assert not os.path.exists(
                    base_path
                ), f"file {filename} found in base directory (should be in run directory)"

            # verify run manager can find the files
            run_manager = dubber.get_run_manager()
            scanned_files = run_manager.scan_run_files(dubber.run_id)

            for filename in test_files:
                assert (
                    filename in scanned_files
                ), f"file {filename} not found by run manager scan"

            print(
                f"✓ integration test passed - all files organized in run directory {dubber.run_id}"
            )

    finally:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


if __name__ == "__main__":
    success = run_tests()

    # run integration test
    try:
        test_file_organization_integration()
        print("✓ integration test passed")
    except Exception as e:
        print(f"✗ integration test failed: {e}")
        success = False

    sys.exit(0 if success else 1)
