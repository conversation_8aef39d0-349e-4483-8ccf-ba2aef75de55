# uuid-based file organization implementation summary

## overview

successfully implemented a comprehensive uuid-based file organization system for the dubbing platform. each dubbing run now generates a unique uuid4 identifier and creates a dedicated directory structure to organize all output files.

## key features implemented

### 1. unique run identification

-   **uuid4 generation**: each dubbing session generates a unique uuid4 identifier (format: "2292263e-3e65-4d45-acdd-6bd1c7faaf08")
-   **run-specific directories**: creates subdirectories named with the uuid inside the main output folder
-   **automatic directory creation**: directories are created automatically during dubber initialization

### 2. comprehensive file organization

all files generated during a dubbing run are now saved in the run-specific uuid directory:

-   **audio files**: dubbed audio chunks, vocals, background audio, final mixed audio
-   **video files**: separated video, final dubbed video
-   **subtitle files**: source and target language .srt files
-   **metadata files**: utterance metadata, preprocessing output
-   **intermediate files**: audio chunks, temporary processing files
-   **debug files**: transcription.txt (when debug mode enabled)
-   **log files**: all logging output directed to run-specific location

### 3. run tracking and management system

-   **run registry**: json-based registry tracking all runs with metadata
-   **run manager class**: comprehensive api for managing runs
-   **file scanning**: automatic discovery of files created during runs
-   **status tracking**: tracks run status (running, completed, failed)
-   **metadata storage**: stores run information including languages, input file, timestamps

### 4. command-line interface enhancements

-   **run id display**: shows generated uuid at start of dubbing process
-   **list runs option**: `--list_runs` flag to display all available runs
-   **run reference**: `--run_id` parameter for referencing specific runs
-   **run directory display**: shows both base and run-specific output directories

### 5. run management utilities

-   **cli tool**: `src/scripts/run_manager_cli.py` for managing runs from command line
-   **run discovery**: scan directories to find existing runs
-   **file listing**: list all files created by specific runs
-   **cleanup functionality**: remove runs from registry and optionally delete files
-   **run summaries**: detailed statistics including file counts and sizes

## files modified/created

### core system modifications

-   `src/core/dubbing.py`: added uuid generation, run manager integration, directory creation
-   `src/core/main.py`: added run id display and list runs functionality
-   `src/core/command_line.py`: added new command-line arguments
-   `src/video_processing/video_processing.py`: fixed hardcoded directory paths

### new files created

-   `src/utils/run_manager.py`: comprehensive run management system
-   `src/scripts/run_manager_cli.py`: command-line interface for run management
-   `tests/test_uuid_file_organization.py`: comprehensive test suite
-   `run_tests.py`: test runner script

## directory structure example

```
data/outputs/
├── runs_registry.json                    # run tracking registry
├── 2292263e-3e65-4d45-acdd-6bd1c7faaf08/ # run 1 directory
│   ├── dubbed_audio_spa.mp3
│   ├── dubbed_vocals.mp3
│   ├── spa.srt
│   ├── eng.srt
│   ├── utterance_metadata.json
│   ├── video_audio.wav
│   ├── video_video.mp4
│   └── transcription.txt
├── a1b2c3d4-e5f6-4789-90ab-123456789012/ # run 2 directory
│   ├── dubbed_audio_fra.mp3
│   ├── dubbed_vocals.mp3
│   ├── fra.srt
│   ├── eng.srt
│   └── utterance_metadata.json
└── ...
```

## usage examples

### basic dubbing with uuid organization

```bash
python src/core/main.py --input_file video.mp4 --target_language spa
# output: dubbing run id: 2292263e-3e65-4d45-acdd-6bd1c7faaf08
# files saved in: data/outputs/2292263e-3e65-4d45-acdd-6bd1c7faaf08/
```

### list all runs

```bash
python src/core/main.py --list_runs
```

### manage runs with cli tool

```bash
# list all runs
python src/scripts/run_manager_cli.py list

# get run information
python src/scripts/run_manager_cli.py info 2292263e-3e65-4d45-acdd-6bd1c7faaf08

# list files for a run
python src/scripts/run_manager_cli.py files 2292263e-3e65-4d45-acdd-6bd1c7faaf08

# cleanup a run
python src/scripts/run_manager_cli.py cleanup 2292263e-3e65-4d45-acdd-6bd1c7faaf08
```

### run tests

```bash
python run_tests.py
```

## benefits achieved

1. **complete isolation**: each dubbing session is completely isolated with its own directory
2. **easy identification**: runs can be easily identified and referenced by their uuid
3. **organized storage**: no more mixed files from different runs in the same directory
4. **run tracking**: comprehensive tracking of all runs with metadata and status
5. **file management**: easy cleanup and management of run-specific files
6. **debugging support**: easier debugging with isolated run directories
7. **scalability**: system scales well with many concurrent or sequential runs

## backward compatibility

the system maintains backward compatibility:

-   existing scripts and workflows continue to work
-   output directory parameter still functions as expected
-   all existing file processing logic remains unchanged
-   cleanup and file management functions work with new structure

## testing

comprehensive test suite created covering:

-   uuid generation and validation
-   directory creation and isolation
-   run registration and tracking
-   file organization verification
-   cleanup functionality
-   integration testing

## conclusion

the uuid-based file organization system successfully addresses all requirements:

-   ✅ generates unique run ids for each dubbing session
-   ✅ creates run-specific directories for complete file isolation
-   ✅ organizes all file types in appropriate run directories
-   ✅ maintains comprehensive run tracking and retrieval capabilities
-   ✅ provides command-line tools for run management
-   ✅ includes thorough testing and validation

the system is now ready for production use and provides a clean, organized, and scalable approach to managing dubbing run outputs.
