#!/usr/bin/env python3
"""command-line interface for managing dubbing runs."""

import argparse
import json
import os
import sys
from pathlib import Path

# add the project root to the path so we can import modules
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.run_manager import RunManager
from src.utils.logger import logger


def list_runs(args):
    """list all runs."""
    run_manager = RunManager(args.output_dir)
    runs = run_manager.list_runs(status_filter=args.status)
    
    if not runs:
        print("no runs found.")
        return
        
    print(f"found {len(runs)} runs:")
    print("-" * 80)
    for run in runs:
        print(f"run id: {run.run_id}")
        print(f"created: {run.created_at}")
        print(f"status: {run.status}")
        print(f"input: {run.input_file}")
        print(f"languages: {run.source_language} -> {run.target_language}")
        print(f"files: {len(run.files_created)} created")
        print("-" * 80)


def get_run_info(args):
    """get detailed information about a specific run."""
    run_manager = RunManager(args.output_dir)
    
    if args.summary:
        summary = run_manager.get_run_summary(args.run_id)
        if summary:
            print(json.dumps(summary, indent=2))
        else:
            print(f"run {args.run_id} not found.")
    else:
        run_info = run_manager.get_run_info(args.run_id)
        if run_info:
            print(f"run id: {run_info.run_id}")
            print(f"created: {run_info.created_at}")
            print(f"status: {run_info.status}")
            print(f"input file: {run_info.input_file}")
            print(f"source language: {run_info.source_language}")
            print(f"target language: {run_info.target_language}")
            print(f"output directory: {run_info.output_directory}")
            print(f"files created: {len(run_info.files_created)}")
            if run_info.files_created:
                print("files:")
                for file_path in run_info.files_created:
                    print(f"  - {file_path}")
        else:
            print(f"run {args.run_id} not found.")


def list_files(args):
    """list files created by a specific run."""
    run_manager = RunManager(args.output_dir)
    files = run_manager.get_run_files(args.run_id)
    
    if not files:
        print(f"no files found for run {args.run_id}.")
        return
        
    print(f"files created by run {args.run_id}:")
    for file_path in files:
        print(f"  - {file_path}")


def discover_runs(args):
    """discover runs by scanning directories."""
    run_manager = RunManager(args.output_dir)
    discovered = run_manager.discover_runs()
    
    if not discovered:
        print("no run directories found.")
        return
        
    print(f"discovered {len(discovered)} run directories:")
    for run_id in discovered:
        files = run_manager.scan_run_files(run_id)
        print(f"  - {run_id} ({len(files)} files)")


def cleanup_run(args):
    """cleanup a specific run."""
    run_manager = RunManager(args.output_dir)
    
    if args.remove_files:
        confirm = input(f"are you sure you want to delete run {args.run_id} and all its files? (y/n): ")
        if confirm.lower() != 'y':
            print("operation cancelled.")
            return
            
    success = run_manager.cleanup_run(args.run_id, remove_files=args.remove_files)
    if success:
        action = "removed" if args.remove_files else "cleaned up"
        print(f"successfully {action} run {args.run_id}.")
    else:
        print(f"failed to cleanup run {args.run_id}.")


def main():
    """main entry point."""
    parser = argparse.ArgumentParser(description="manage dubbing runs")
    parser.add_argument(
        "--output-dir",
        default="data/outputs/",
        help="base output directory (default: data/outputs/)"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="available commands")
    
    # list command
    list_parser = subparsers.add_parser("list", help="list all runs")
    list_parser.add_argument(
        "--status",
        choices=["running", "completed", "failed"],
        help="filter by status"
    )
    list_parser.set_defaults(func=list_runs)
    
    # info command
    info_parser = subparsers.add_parser("info", help="get run information")
    info_parser.add_argument("run_id", help="run id to get info for")
    info_parser.add_argument(
        "--summary",
        action="store_true",
        help="show detailed summary with file statistics"
    )
    info_parser.set_defaults(func=get_run_info)
    
    # files command
    files_parser = subparsers.add_parser("files", help="list files for a run")
    files_parser.add_argument("run_id", help="run id to list files for")
    files_parser.set_defaults(func=list_files)
    
    # discover command
    discover_parser = subparsers.add_parser("discover", help="discover runs by scanning directories")
    discover_parser.set_defaults(func=discover_runs)
    
    # cleanup command
    cleanup_parser = subparsers.add_parser("cleanup", help="cleanup a run")
    cleanup_parser.add_argument("run_id", help="run id to cleanup")
    cleanup_parser.add_argument(
        "--remove-files",
        action="store_true",
        help="also remove all files (dangerous!)"
    )
    cleanup_parser.set_defaults(func=cleanup_run)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    # ensure output directory exists
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        args.func(args)
    except Exception as e:
        logger().error(f"error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
