#!/usr/bin/env python3
"""test runner for uuid-based file organization system."""

import sys
import os
from pathlib import Path

# add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """run all tests for the uuid-based file organization system."""
    print("running uuid-based file organization tests...")
    print("=" * 60)
    
    # run the main test suite
    try:
        from tests.test_uuid_file_organization import run_tests, test_file_organization_integration
        
        print("running unit tests...")
        unit_tests_passed = run_tests()
        
        print("\nrunning integration test...")
        try:
            test_file_organization_integration()
            print("✓ integration test passed")
            integration_test_passed = True
        except Exception as e:
            print(f"✗ integration test failed: {e}")
            integration_test_passed = False
            
        print("\n" + "=" * 60)
        if unit_tests_passed and integration_test_passed:
            print("✓ all tests passed! uuid-based file organization is working correctly.")
            return True
        else:
            print("✗ some tests failed. please check the output above.")
            return False
            
    except ImportError as e:
        print(f"error importing test modules: {e}")
        print("make sure all dependencies are installed and the project structure is correct.")
        return False
    except Exception as e:
        print(f"unexpected error running tests: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
